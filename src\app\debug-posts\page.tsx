'use client'

import { useEffect, useState } from 'react'
import { getBlogPosts } from '@/lib/firebase-operations'
import { collection, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export default function DebugPostsPage() {
  const [posts, setPosts] = useState<any[]>([])
  const [allPosts, setAllPosts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    async function loadPosts() {
      try {
        // Get published posts using the normal function
        const publishedPosts = await getBlogPosts()
        setPosts(publishedPosts)

        // Get ALL posts directly from Firestore (including unpublished)
        const querySnapshot = await getDocs(collection(db, 'blog_posts'))
        const allPostsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        setAllPosts(allPostsData)

      } catch (error: any) {
        console.error('Error loading posts:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    loadPosts()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading posts...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Blog Posts Debug
          </h1>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-6">
              Error: {error}
            </div>
          )}

          <div className="space-y-8">
            {/* Published Posts */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Published Posts ({posts.length})
              </h2>
              {posts.length === 0 ? (
                <p className="text-gray-600 dark:text-gray-400">No published posts found.</p>
              ) : (
                <div className="space-y-4">
                  {posts.map((post, index) => (
                    <div key={post.id || index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white">{post.title || 'No title'}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">ID: {post.id}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Published: {post.published ? 'Yes' : 'No'}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Author ID: {post.author_id}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Created: {post.created_at}</p>
                      {post.excerpt && (
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-2">{post.excerpt}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* All Posts */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                All Posts in Database ({allPosts.length})
              </h2>
              {allPosts.length === 0 ? (
                <p className="text-gray-600 dark:text-gray-400">No posts found in database.</p>
              ) : (
                <div className="space-y-4">
                  {allPosts.map((post, index) => (
                    <div key={post.id || index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white">{post.title || 'No title'}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">ID: {post.id}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Published: {post.published ? 'Yes' : 'No'}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Author ID: {post.author_id}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Slug: {post.slug}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Created: {post.created_at?.toDate?.()?.toISOString() || post.created_at}</p>
                      {post.excerpt && (
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-2">{post.excerpt}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="mt-8 text-center">
            <a
              href="/"
              className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              ← Back to website
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
