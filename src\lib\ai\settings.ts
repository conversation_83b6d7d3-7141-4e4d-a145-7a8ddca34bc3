// AI Settings Storage with Firebase
'use client'

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  deleteDoc
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { AISettings, DEFAULT_AI_SETTINGS, TokenUsage, CacheEntry } from '@/types/ai'

// AI Settings Operations
export async function saveAISettings(
  userId: string,
  settings: Omit<AISettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>
): Promise<string> {
  try {
    // Check if user already has settings
    const existingSettings = await getUserAISettings(userId)

    if (existingSettings) {
      // Update existing settings
      await updateAISettings(existingSettings.id!, settings)
      return existingSettings.id!
    } else {
      // Create new settings
      const docRef = await addDoc(collection(db, 'ai_settings'), {
        ...settings,
        user_id: userId,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      })
      return docRef.id
    }
  } catch (error) {
    console.error('Error saving AI settings:', error)
    throw error
  }
}

export async function getUserAISettings(userId: string): Promise<AISettings | null> {
  try {
    const q = query(
      collection(db, 'ai_settings'),
      where('user_id', '==', userId),
      orderBy('updated_at', 'desc')
    )

    const querySnapshot = await getDocs(q)
    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    const data = doc.data() as any
    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    } as AISettings
  } catch (error) {
    console.error('Error getting AI settings:', error)
    return null
  }
}

export async function updateAISettings(
  settingsId: string,
  updates: Partial<AISettings>
): Promise<void> {
  try {
    const settingsRef = doc(db, 'ai_settings', settingsId)
    await updateDoc(settingsRef, {
      ...updates,
      updated_at: serverTimestamp(),
    })
  } catch (error) {
    console.error('Error updating AI settings:', error)
    throw error
  }
}

export async function getOrCreateAISettings(userId: string): Promise<AISettings> {
  try {
    let settings = await getUserAISettings(userId)
    
    if (!settings) {
      // Create default settings for new user
      const settingsId = await saveAISettings(userId, DEFAULT_AI_SETTINGS)
      settings = await getUserAISettings(userId)
      
      if (!settings) {
        throw new Error('Failed to create default AI settings')
      }
    }
    
    return settings
  } catch (error) {
    console.error('Error getting or creating AI settings:', error)
    throw error
  }
}

// Token Usage Tracking
export async function saveTokenUsage(usage: Omit<TokenUsage, 'timestamp'>): Promise<void> {
  try {
    await addDoc(collection(db, 'token_usage'), {
      ...usage,
      timestamp: serverTimestamp(),
    })
  } catch (error) {
    console.error('Error saving token usage:', error)
    // Don't throw error for usage tracking failures
  }
}

export async function getUserTokenUsage(
  userId: string,
  startDate?: Date,
  endDate?: Date
): Promise<TokenUsage[]> {
  try {
    let q = query(
      collection(db, 'token_usage'),
      where('user_id', '==', userId),
      orderBy('timestamp', 'desc')
    )

    const querySnapshot = await getDocs(q)
    let usageData = querySnapshot.docs.map(doc => {
      const data = doc.data() as any
      return {
        ...data,
        timestamp: data.timestamp?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as TokenUsage
    })

    // Filter by date range if provided
    if (startDate || endDate) {
      usageData = usageData.filter(usage => {
        const usageDate = new Date(usage.timestamp)
        if (startDate && usageDate < startDate) return false
        if (endDate && usageDate > endDate) return false
        return true
      })
    }

    return usageData
  } catch (error) {
    console.error('Error getting token usage:', error)
    return []
  }
}

export async function getTotalCostForUser(userId: string, days: number = 30): Promise<number> {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const usage = await getUserTokenUsage(userId, startDate)
    return usage.reduce((total, record) => total + record.cost, 0)
  } catch (error) {
    console.error('Error calculating total cost:', error)
    return 0
  }
}

// Enhanced Analytics Functions
export interface ProviderUsageStats {
  provider: string
  totalTokens: number
  totalCost: number
  requestCount: number
  averageCostPerRequest: number
  models: {
    [modelId: string]: {
      totalTokens: number
      totalCost: number
      requestCount: number
    }
  }
}

export interface MonthlyUsageData {
  month: string // YYYY-MM format
  providers: {
    [provider: string]: {
      totalTokens: number
      totalCost: number
      requestCount: number
    }
  }
  totalCost: number
  totalTokens: number
}

export interface TopModelUsage {
  provider: string
  model: string
  totalTokens: number
  totalCost: number
  requestCount: number
}

export async function getProviderUsageStats(
  userId: string,
  startDate?: Date,
  endDate?: Date
): Promise<ProviderUsageStats[]> {
  try {
    const usage = await getUserTokenUsage(userId, startDate, endDate)
    const providerStats: { [provider: string]: ProviderUsageStats } = {}

    usage.forEach(record => {
      if (!providerStats[record.provider]) {
        providerStats[record.provider] = {
          provider: record.provider,
          totalTokens: 0,
          totalCost: 0,
          requestCount: 0,
          averageCostPerRequest: 0,
          models: {}
        }
      }

      const stats = providerStats[record.provider]
      stats.totalTokens += record.totalTokens
      stats.totalCost += record.cost
      stats.requestCount += 1

      // Track model-specific stats
      if (!stats.models[record.model]) {
        stats.models[record.model] = {
          totalTokens: 0,
          totalCost: 0,
          requestCount: 0
        }
      }

      stats.models[record.model].totalTokens += record.totalTokens
      stats.models[record.model].totalCost += record.cost
      stats.models[record.model].requestCount += 1
    })

    // Calculate averages
    Object.values(providerStats).forEach(stats => {
      stats.averageCostPerRequest = stats.requestCount > 0
        ? stats.totalCost / stats.requestCount
        : 0
    })

    return Object.values(providerStats)
  } catch (error) {
    console.error('Error getting provider usage stats:', error)
    return []
  }
}

export async function getMonthlyUsageData(
  userId: string,
  months: number = 12
): Promise<MonthlyUsageData[]> {
  try {
    const endDate = new Date()
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - months)

    const usage = await getUserTokenUsage(userId, startDate, endDate)
    const monthlyData: { [month: string]: MonthlyUsageData } = {}

    usage.forEach(record => {
      const date = new Date(record.timestamp)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthKey,
          providers: {},
          totalCost: 0,
          totalTokens: 0
        }
      }

      const monthData = monthlyData[monthKey]

      if (!monthData.providers[record.provider]) {
        monthData.providers[record.provider] = {
          totalTokens: 0,
          totalCost: 0,
          requestCount: 0
        }
      }

      monthData.providers[record.provider].totalTokens += record.totalTokens
      monthData.providers[record.provider].totalCost += record.cost
      monthData.providers[record.provider].requestCount += 1

      monthData.totalCost += record.cost
      monthData.totalTokens += record.totalTokens
    })

    // Sort by month and fill missing months with zero data
    const result: MonthlyUsageData[] = []
    for (let i = months - 1; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

      result.push(monthlyData[monthKey] || {
        month: monthKey,
        providers: {},
        totalCost: 0,
        totalTokens: 0
      })
    }

    return result
  } catch (error) {
    console.error('Error getting monthly usage data:', error)
    return []
  }
}

export async function getTopModels(
  userId: string,
  limit: number = 3,
  startDate?: Date,
  endDate?: Date
): Promise<TopModelUsage[]> {
  try {
    const usage = await getUserTokenUsage(userId, startDate, endDate)
    const modelStats: { [key: string]: TopModelUsage } = {}

    usage.forEach(record => {
      const key = `${record.provider}:${record.model}`

      if (!modelStats[key]) {
        modelStats[key] = {
          provider: record.provider,
          model: record.model,
          totalTokens: 0,
          totalCost: 0,
          requestCount: 0
        }
      }

      modelStats[key].totalTokens += record.totalTokens
      modelStats[key].totalCost += record.cost
      modelStats[key].requestCount += 1
    })

    return Object.values(modelStats)
      .sort((a, b) => b.totalCost - a.totalCost)
      .slice(0, limit)
  } catch (error) {
    console.error('Error getting top models:', error)
    return []
  }
}

// Cache Management
export async function saveCacheEntry(entry: Omit<CacheEntry, 'expiresAt'>): Promise<void> {
  try {
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 24) // Cache for 24 hours

    await addDoc(collection(db, 'ai_cache'), {
      ...entry,
      expiresAt: expiresAt.toISOString(),
    })
  } catch (error) {
    console.error('Error saving cache entry:', error)
    // Don't throw error for cache failures
  }
}

export async function getCacheEntry(key: string): Promise<CacheEntry | null> {
  try {
    const q = query(
      collection(db, 'ai_cache'),
      where('key', '==', key)
    )

    const querySnapshot = await getDocs(q)
    if (querySnapshot.empty) {
      return null
    }

    const doc = querySnapshot.docs[0]
    const data = doc.data() as CacheEntry

    // Check if cache entry is expired
    const now = new Date()
    const expiresAt = new Date(data.expiresAt)
    
    if (now > expiresAt) {
      // Delete expired entry
      await deleteDoc(doc.ref)
      return null
    }

    return data
  } catch (error) {
    console.error('Error getting cache entry:', error)
    return null
  }
}

export async function clearExpiredCache(): Promise<void> {
  try {
    const now = new Date().toISOString()
    const q = query(
      collection(db, 'ai_cache'),
      where('expiresAt', '<', now)
    )

    const querySnapshot = await getDocs(q)
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref))
    
    await Promise.all(deletePromises)
    console.log(`Cleared ${deletePromises.length} expired cache entries`)
  } catch (error) {
    console.error('Error clearing expired cache:', error)
  }
}

// Utility functions for settings management
export function validateAISettings(settings: Partial<AISettings>): string[] {
  const errors: string[] = []

  if (settings.preferences) {
    const { preferences } = settings
    
    if (preferences.maxTokensPerRequest && preferences.maxTokensPerRequest < 100) {
      errors.push('Max tokens per request must be at least 100')
    }
    
    if (preferences.maxTokensPerRequest && preferences.maxTokensPerRequest > 100000) {
      errors.push('Max tokens per request cannot exceed 100,000')
    }
    
    if (preferences.temperature !== undefined && (preferences.temperature < 0 || preferences.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2')
    }
    
    if (preferences.costLimit && preferences.costLimit < 0) {
      errors.push('Cost limit cannot be negative')
    }
  }

  return errors
}

export function createCacheKey(provider: string, model: string, prompt: string): string {
  // Create a hash-like key for caching
  const content = `${provider}:${model}:${prompt}`
  return btoa(content).replace(/[^a-zA-Z0-9]/g, '').substring(0, 50)
}
